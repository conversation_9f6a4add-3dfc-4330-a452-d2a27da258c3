import { Processor } from '@/types/processors';

export const PAYMENT_PROCESSORS: Processor[] = [
  {
    id: 'globalpay_propay',
    name: 'GlobalPay - ProPay',
    type: 'PAYMENT',
    logo: '/icons/processors/globalpay.png',
    enabled: true,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
      apiKey: true,
    },
    config: {
      clientId: 'gpp_client_2a4b6c8d0e',
      clientSecret: 'gpp_secret_1f3e5d7c9b',
      apiKey: 'gpp_key_8h6g4f2d0b',
    },
    stats: {
      totalTransactions: 45678,
      successfulTransactions: 44987,
      revenue: 789123.45,
      lastSync: new Date('2024-01-15T11:45:00'),
      conversionRate: 98.4,
    },
  },
  {
    id: 'globalpay_tsys',
    name: 'GlobalPay - Transit (TSYS)',
    type: 'PAYMENT',
    logo: '/icons/processors/globalpay.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
      apiKey: true,
    },
  },
  {
    id: 'elavon',
    name: 'Elavon',
    type: 'PAYMENT',
    logo: '/icons/processors/elavon.png',
    enabled: true,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
    config: {
      clientId: 'ela_client_7k9m5n3p1q',
      clientSecret: 'ela_secret_2w4y6u8i0o',
    },
    stats: {
      totalTransactions: 34567,
      successfulTransactions: 33890,
      revenue: 567234.89,
      lastSync: new Date('2024-01-15T09:20:00'),
      conversionRate: 97.8,
    },
  },
  {
    id: 'Fiserv',
    name: 'Fiserv',
    type: 'PAYMENT',
    logo: '/icons/processors/fiserv.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'worldpay',
    name: 'Worldpay',
    type: 'PAYMENT',
    logo: '/icons/processors/worldpay.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
      apiKey: true,
    },
  },
];

export const CRM_PROCESSORS: Processor[] = [
  {
    id: 'gohighlevel',
    name: 'GoHighLevel',
    type: 'CRM',
    logo: '/icons/crm/gohighlevel.png',
    enabled: true,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
    config: {
      clientId: 'ghl_client_5f9b2e3a1c',
      clientSecret: 'ghl_secret_8d7c6b4a9e',
    },
    stats: {
      totalTransactions: 15234,
      successfulTransactions: 14891,
      revenue: 256789.5,
      lastSync: new Date('2024-01-15T08:30:00'),
      activeCustomers: 4567,
    },
  },
  {
    id: 'hubspot',
    name: 'HubSpot',
    type: 'CRM',
    logo: '/icons/crm/hubspot.png',
    enabled: true,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
    config: {
      apiKey: 'pat-na1-3d4f5g6h7i8j9k',
    },
    stats: {
      totalTransactions: 28976,
      successfulTransactions: 28102,
      revenue: 489234.75,
      lastSync: new Date('2024-01-15T10:15:00'),
      activeCustomers: 8932,
    },
  },
  {
    id: 'salesforce',
    name: 'Salesforce',
    type: 'CRM',
    logo: '/icons/crm/salesforce.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'zoho',
    name: 'Zoho',
    type: 'CRM',
    logo: '/icons/crm/zoho.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'zendesk',
    name: 'Zendesk',
    type: 'CRM',
    logo: '/icons/crm/zendesk.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
  {
    id: 'pipedrive',
    name: 'Pipedrive',
    type: 'CRM',
    logo: '/icons/crm/pipedrive.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
];

export const POS_PROCESSORS: Processor[] = [
  {
    id: 'square',
    name: 'Square',
    type: 'POS',
    logo: '/icons/pos/square.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'clover',
    name: 'Clover',
    type: 'POS',
    logo: '/icons/pos/clover.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
  {
    id: 'toast',
    name: 'Toast',
    type: 'POS',
    logo: '/icons/pos/toast.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'skytab',
    name: 'SkyTab',
    type: 'POS',
    logo: '/icons/pos/skytab.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
  {
    id: 'lightspeed',
    name: 'Lightspeed',
    type: 'POS',
    logo: '/icons/pos/lightspeed.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'swiftpos',
    name: 'SwiftPOS',
    type: 'POS',
    logo: '/icons/pos/swiftpos.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
];
