import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HiDocumentText,
  HiCreditCard,
  HiExclamation,
} from 'react-icons/hi';
import { Button, Modal, Timeline } from 'flowbite-react';
import { useEffect, useMemo, useState } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import {
  Gateway_DisputeDocument,
  Gateway_SubmitDisputeDocumentDocument,
  Gateway_SubmitDisputeDocumentInputData,
  Gateway_SubmitDisputeDocumentInputDataFiles,
  Gateway_UploadDisputeDocumentDocument,
  GatewayUniDisputeOutput,
} from '@/graphql/generated/graphql';
import { getGatewayUniDisputeOutputStatusChip } from '../page/disputes-tab';
import { StatusChip } from '@/components/globals';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import {
  ProductListSection,
  ProductListSectionProps,
} from '../../products/components/product-list-section';
import { GeneralDetailsSection } from '@/components/globals/general-details-section';
import { message, parseFilesToBase64 } from '@/components/shared/utils';
import { DisputeChallengeDocumentParams } from '../utils';
import { File_upload } from '@/graphql/declarations/file';
import { int2DecToFloat, moneyFormat } from '@/lib/utils';
import React from 'react';
import moment from 'moment';
import { mockDisputes } from '@/mock/disputes-data';

type DisputeDetailsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onUpload?: (files: FileList) => void;
  queryData: {
    caseID: string;
    groupID: string;
  };
};

// Extended type to include file URL
type ExtendedDisputeDocumentParams = DisputeChallengeDocumentParams & {
  url?: string;
  itemID?: string;
};

// Extended type for the dispute data that includes additional fields
interface ExtendedDisputeData extends Partial<GatewayUniDisputeOutput> {
  createdAt?: string;
  merchantChargebackDate?: string;
  merchantRepresentmentDate?: string;
  challenge?: {
    error_code?: string;
    detailed_error_code?: string;
    detailed_error_description?: string;
  };
}

type HistoryItem = {
  action: string;
  body: string;
  actor: string;
  createdAt: string;
  icon: React.ComponentType;
  type: string;
  details?: {
    error_code?: string;
    detailed_error_code?: string;
    detailed_error_description?: string;
    status?: string;
    document_name?: string;
    action_type?: string;
    action_time?: string;
    action_result?: string;
    file_url?: string;
  } | null;
};

// Function to format dates in "Month 1, Year, 5:30 PM" format
const formatDateTime = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  return moment(date).format('MMMM D, YYYY, h:mm A');
};

function historyItemActionTitle(action?: string) {
  if (!action) return '';
  switch (action.toUpperCase()) {
    case 'DAYS_TO_REPRESENT_ZERO':
      return 'DOCUMENT SUBMISSION DEADLINE';
    case 'REPRESENTMENT_DATE':
      return 'DISPUTE DOCUMENT SUBMITTED';
    default:
      return action.toUpperCase();
  }
}

const DisputeDetailsModal = ({ isOpen, onClose, queryData }: DisputeDetailsModalProps) => {
  const { caseID, groupID } = queryData;

  const [files, setFiles] = useState<DisputeChallengeDocumentParams[]>([]);
  const [uploadedItemIDs, setUploadedItemIDs] = useState<string[]>([]);
  const [serverFileIDs, setServerFileIDs] = useState<Record<string, string>>({});
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [selectedDocumentType, setSelectedDocumentType] = useState<string>('dispute_document');
  const [showActionButtons, setShowActionButtons] = useState(false);

  const router = useRouter();

  // Query for real dispute data first
  const {
    data,
    loading: disputeDataLoading,
    error: disputeDataError,
    refetch: refetchDisputeData,
  } = useQuery(Gateway_DisputeDocument, {
    variables: {
      input: {
        data: {
          caseID,
        },
        groupID,
      },
    },
    skip: !caseID || !groupID,
  });

  // If no real data, use mock data
  const disputeData = useMemo(() => {
    if (data?.gateway_dispute) {
      return data.gateway_dispute;
    }
    // Find the dispute in mock data if no real data
    return mockDisputes.find((d) => d.caseID === caseID);
  }, [data, caseID]);

  const [status, label] = getGatewayUniDisputeOutputStatusChip(disputeData?.status ?? '');

  const [uploadDocumentMutation, { loading: uploadDocumentLoading }] = useMutation(
    Gateway_SubmitDisputeDocumentDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successCreate('Challenge dispute'));
        setFiles([]);
        refetchDisputeData();
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('Challenge dispute', error.message));
      },
    },
  );

  const [uploadDisputeFileMutation, { loading: uploadDisputeFileLoading }] = useMutation(
    Gateway_UploadDisputeDocumentDocument,
    {
      onCompleted: (data) => {
        if (data?.gateway_uploadDisputeDocument?.itemID) {
          setUploadedItemIDs([...uploadedItemIDs, data.gateway_uploadDisputeDocument.itemID]);
          toast.success('File uploaded successfully');
          // refetchDisputeData();
        }
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('File upload error', error.message));
      },
    },
  );

  // Add File_upload mutation
  const [fileUploadMutation, { loading: fileUploadLoading }] = useMutation(File_upload, {
    onError: (error) => {
      toast.error(message.api.errorCreate('File upload error', error.message));
    },
  });

  // Load server file IDs when disputeData changes
  useEffect(() => {
    if (disputeData?.files && disputeData.files.length > 0) {
      const fileIDMap: Record<string, string> = {};

      // For each file in disputeData, create an ID mapping
      disputeData.files.forEach((file, index) => {
        if (file?.fileUrl && file?.id) {
          // Create a mapping using the server-index as key and file ID as value
          fileIDMap[`server-${index}`] = file.id;
        }
      });

      setServerFileIDs(fileIDMap);
    }
  }, [disputeData?.files, files]);

  const handleMockChallengeSubmit = async () => {
    try {
      if (selectedFiles.length === 0) {
        toast.warning('Please select at least one file to submit');
        return;
      }

      // Simulate challenge submission
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate network delay

      toast.success('Dispute challenge submitted successfully (Mock)');
      setFiles([]);
      setSelectedFiles([]);

      return true;
    } catch (error) {
      console.error('Error in mock challenge submission:', error);
      toast.error('Failed to submit challenge (Mock)');
      return false;
    }
  };

  const onChanllengeSubmit = async () => {
    // If we're in mock mode, use mock challenge submission
    if (!data?.gateway_dispute) {
      return handleMockChallengeSubmit();
    }

    try {
      // Real challenge submission logic
      if (selectedFiles.length === 0) {
        toast.warning('Please select at least one file to submit');
        return;
      }

      // Collect itemIDs of selected files from the local state
      const selectedLocalFiles = files
        .filter((file) => file.itemID && selectedFiles.includes(String(file.itemID)))
        .map((file) => file.itemID as string);

      // Collect itemIDs of selected server files using our serverFileIDs map
      const selectedServerFileIDs = selectedFiles
        .filter((id) => id.startsWith('server-'))
        .map((id) => serverFileIDs[id])
        .filter(Boolean); // Filter out undefined values

      // Combine all selected itemIDs
      const allSelectedItemIDs = [...selectedLocalFiles, ...selectedServerFileIDs];

      if (allSelectedItemIDs.length === 0) {
        toast.warning('No valid files selected. Please select files that have been uploaded.');
        return;
      }

      // Submit the challenge with the selected itemIDs
      const uploadDisputeChallengeDocumentParams: Gateway_SubmitDisputeDocumentInputData = {
        disputeID: disputeData?.disputeID ?? '',
        files: allSelectedItemIDs.map((itemID) => {
          const fileData: Gateway_SubmitDisputeDocumentInputDataFiles = {
            itemID: itemID,
          };
          return fileData;
        }),
      };

      await uploadDocumentMutation({
        variables: {
          input: {
            groupID,
            data: {
              ...uploadDisputeChallengeDocumentParams,
            },
          },
        },
      });

      // Success is handled in the mutation's onCompleted callback
    } catch (error) {
      console.error('Upload dispute document error: ', error);
      toast.error('Failed to challenge dispute. Please try again.');
    }
  };

  // Handle mock file upload
  const handleMockFileUpload = async (file: File, type: string) => {
    try {
      const mockFileUrl = `https://example.com/files/${Math.random().toString(36).substr(2, 9)}.pdf`;
      const mockItemID = `MOCK-FILE-${Math.random().toString(36).substr(2, 9)}`;

      // Add the file to state with mock URL and ID
      const newFile: ExtendedDisputeDocumentParams = {
        file,
        type,
        url: mockFileUrl,
        itemID: mockItemID,
      };

      setFiles([...files, newFile]);
      setUploadedItemIDs([...uploadedItemIDs, mockItemID]);
      toast.success('File uploaded successfully (Mock)');

      return true;
    } catch (error) {
      console.error('Error in mock file upload:', error);
      toast.error('Failed to upload file (Mock)');
      return false;
    }
  };

  const handleUpload = async (file: File, type: string) => {
    // If we're in mock mode, use mock file upload
    if (!data?.gateway_dispute) {
      return handleMockFileUpload(file, type);
    }

    try {
      // Convert file to base64
      const [base64Data] = await parseFilesToBase64([file]);

      // Upload the file using File_upload mutation
      const uploadResult = await fileUploadMutation({
        variables: {
          input: {
            files: [
              {
                b64: base64Data.b64,
                filename: file.name,
                mimetype: file.type,
              },
            ],
          },
        },
      });

      // Get the URL from the response
      const fileUrl = uploadResult.data?.file_upload?.files?.[0]?.url;

      if (fileUrl) {
        // Add the file to the state with the URL
        const newFile: ExtendedDisputeDocumentParams = {
          file,
          type,
          url: fileUrl,
        };

        setFiles([...files, newFile]);

        // Now use the URL to register the file in the dispute system
        const disputeUploadResult = await uploadDisputeFileMutation({
          variables: {
            input: {
              groupID,
              data: {
                disputeID: disputeData?.disputeID ?? '',
                upload: {
                  url: fileUrl,
                  size: file.size,
                  type: type,
                  filetype: file.type,
                  purpose: type,
                },
              },
            },
          },
        });

        // Store the itemID
        if (disputeUploadResult.data?.gateway_uploadDisputeDocument?.itemID) {
          const itemID = disputeUploadResult.data.gateway_uploadDisputeDocument.itemID;
          setUploadedItemIDs([...uploadedItemIDs, itemID]);

          // Update the file in the state with the itemID
          const updatedFiles = [...files, { ...newFile, itemID }];
          setFiles(updatedFiles);
        }
      } else {
        // If no URL was returned, still add the file to the state
        setFiles([...files, { file, type }]);
        toast.warning(
          'File uploaded but no URL was returned. The file may not be properly linked.',
        );
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      // Still add the file to the state even if upload failed
      setFiles([...files, { file, type }]);
      toast.error('Failed to upload file. You can try again later.');
    }
  };

  const requestDocument = async (type: string) => {
    // If we're in mock mode, use mock document request
    if (!data?.gateway_dispute) {
      return handleMockRequestDocument(type);
    }

    try {
      // Real document request logic
      await uploadDisputeFileMutation({
        variables: {
          input: {
            groupID,
            data: {
              disputeID: disputeData?.disputeID ?? '',
              request: {
                flag: type,
              },
            },
          },
        },
      });

      toast.success('Dispute document requested successfully');
      await refetchDisputeData();
    } catch (error) {
      console.error('Error requesting document:', error);
      toast.error('Failed to request document');
    }
  };

  // Mock function to handle document requests
  const handleMockRequestDocument = async (type: string) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate network delay
      toast.success('Document request submitted successfully (Mock)');
      return true;
    } catch (error) {
      console.error('Error in mock document request:', error);
      toast.error('Failed to request document (Mock)');
      return false;
    }
  };

  // Mock function to handle file deletion
  const handleMockDeleteFile = async (itemID: string) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate network delay

      // Remove the file from state
      setFiles(files.filter((file) => file.itemID !== itemID));
      setUploadedItemIDs(uploadedItemIDs.filter((id) => id !== itemID));

      toast.success('File deleted successfully (Mock)');
      return true;
    } catch (error) {
      console.error('Error in mock file deletion:', error);
      toast.error('Failed to delete file (Mock)');
      return false;
    }
  };

  const handleDeleteFile = async (itemID: string) => {
    // If we're in mock mode, use mock file deletion
    if (!data?.gateway_dispute) {
      return handleMockDeleteFile(itemID);
    }

    try {
      // Real file deletion logic
      const deleteResult = await uploadDisputeFileMutation({
        variables: {
          input: {
            groupID,
            data: {
              disputeID: disputeData?.disputeID ?? '',
              delete: {
                itemID,
              },
            },
          },
        },
      });

      if (deleteResult.data?.gateway_uploadDisputeDocument?.itemID) {
        // Remove the file from the state
        setFiles(files.filter((file) => file.itemID !== itemID));
        setUploadedItemIDs(uploadedItemIDs.filter((id) => id !== itemID));
        toast.success('File deleted successfully');
        // refetchDisputeData();
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error('Failed to delete file');
    }
  };

  // Function to handle deleting a server file without an itemID
  const handleDeleteServerFile = async (id?: string | null) => {
    try {
      if (id) {
        // If we have the itemID in our local state, use it for deletion
        await handleDeleteFile(id);
      } else {
        // If we don't have the itemID, we need to inform the user
        toast.info(
          'This file cannot be deleted directly. Please try refreshing or contact support.',
        );
      }
    } catch (error) {
      console.error('Error deleting server file:', error);
      toast.error('Failed to delete file');
    }
  };

  // Function to handle deleting a queued file (not yet submitted)
  const handleDeleteQueuedFile = (index: number) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
    toast.info('File removed from queue');
  };

  // Toggle file selection
  const toggleFileSelection = (index: string) => {
    setSelectedFiles((prev) => {
      if (prev.includes(index)) {
        return prev.filter((i) => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  // Generate history items from dispute data
  const generateHistoryItems = useMemo(() => {
    if (!disputeData?.history || disputeData.history.length === 0) {
      return [];
    }

    // Map the history items
    const historyItems = disputeData.history.map((historyItem) => {
      if (!historyItem) {
        return {
          action: 'Unknown Action',
          body: '',
          actor: 'System',
          createdAt: new Date().toISOString(),
          icon: HiCalendar,
          type: 'default',
          details: null,
        };
      }

      let parsedBody: Record<string, any> = {};
      let type = 'default';
      let icon = HiCalendar;
      let details: HistoryItem['details'] = null;

      // Try to parse the JSON body
      try {
        if (historyItem.body) {
          parsedBody = JSON.parse(historyItem.body);

          // Determine the type based on the content
          if ('challenge' in parsedBody && parsedBody.challenge) {
            type = 'challenge';
            icon = HiExclamation;

            // Check if it's a successful challenge
            if (parsedBody.challenge.status === 'SUCCESS') {
              type = 'challenge-success';
              icon = HiCheck;
              details = {
                status: parsedBody.challenge.status,
                document_name: parsedBody.challenge.document_name,
                action_type: parsedBody.challenge.action?.type,
                action_time: parsedBody.challenge.action?.time_created,
                action_result: parsedBody.challenge.action?.result_code,
                file_url: parsedBody.file?.fileUrl,
              };
            } else {
              // Handle error challenge
              details = {
                error_code: parsedBody.challenge.error_code,
                detailed_error_code: parsedBody.challenge.detailed_error_code,
                detailed_error_description: parsedBody.challenge.detailed_error_description,
              };
            }
          } else if ('file' in parsedBody) {
            type = 'file';
            icon = HiDocumentText;
          } else if (historyItem.action?.toLowerCase().includes('chargeback')) {
            type = 'chargeback';
            icon = HiCreditCard;
          } else if (historyItem.action?.toLowerCase().includes('representment')) {
            type = 'representment';
            icon = HiUpload;
          } else if (historyItem.action?.toLowerCase().includes('dispute')) {
            type = 'dispute';
            icon = HiExclamation;
          } else {
            // Generic event for any other type of event
            type = 'generic';
            icon = HiCalendar;

            // Extract key information from the parsed body for display
            const genericDetails: Record<string, any> = {};

            // Iterate through the parsed body and extract relevant information
            Object.entries(parsedBody).forEach(([key, value]) => {
              // Format monetary values
              if (
                (key === 'amount' ||
                  key === 'transactionAmount' ||
                  key.toLowerCase().includes('amount')) &&
                typeof value === 'number'
              ) {
                // Format as money
                genericDetails[key] = moneyFormat(int2DecToFloat(value));
              }
              // Handle days to represent field
              else if (key === 'daysToRepresent' && typeof value === 'number') {
                genericDetails[key] = String(value);
                // Add a warning indicator for low days
                if (value < 5) {
                  genericDetails[`${key}_warning`] = 'Urgent: Less than 5 days remaining';
                }
              }
              // Skip complex nested objects, focus on simple values
              else if (typeof value !== 'object' || value === null) {
                // Store simple values directly
                genericDetails[key] = value;
              } else if (Array.isArray(value)) {
                // For arrays, provide a summary with length
                if (value.length === 0) {
                  genericDetails[key] = 'Empty array';
                } else {
                  genericDetails[key] =
                    `Array with ${value.length} item${value.length > 1 ? 's' : ''}`;
                }
              } else {
                // For objects, try to extract key information
                const objKeys = Object.keys(value);
                if (objKeys.length === 0) {
                  genericDetails[key] = 'Empty object';
                } else if ('id' in value) {
                  // If the object has an ID, include it
                  genericDetails[`${key}_id`] = value.id;
                }

                // Check for common fields that might be useful
                ['status', 'type', 'name', 'code', 'result', 'date', 'time'].forEach(
                  (commonField) => {
                    if (commonField in value) {
                      genericDetails[`${key}_${commonField}`] = value[commonField];
                    }
                  },
                );

                // Format monetary values in nested objects
                ['amount', 'transactionAmount'].forEach((amountField) => {
                  if (amountField in value && typeof value[amountField] === 'number') {
                    genericDetails[`${key}_${amountField}`] = moneyFormat(
                      int2DecToFloat(value[amountField]),
                    );
                  }
                });
              }
            });

            // Store the generic details
            details = genericDetails;
          }
        }
      } catch (error) {
        console.error('Error parsing history item body:', error);
      }

      // Format the body for display
      let formattedBody = historyItem.body || '';
      if (typeof parsedBody === 'object' && Object.keys(parsedBody).length > 0) {
        // Create a more readable version of the JSON
        formattedBody = Object.entries(parsedBody)
          .filter(([key]) => key !== 'challenge' && key !== 'file') // Filter out the detailed objects we're handling separately
          .map(([key, value]) => {
            if (typeof value === 'object' && value !== null) {
              return `${key}: ${JSON.stringify(value, null, 2)}`;
            }
            return `${key}: ${value}`;
          })
          .join('\n');
      }

      return {
        action: historyItem.action || 'Unknown Action',
        body: formattedBody,
        actor: historyItem.actor || 'System',
        createdAt: historyItem.createdAt || new Date().toISOString(),
        icon,
        type,
        details,
      };
    });

    // Sort history items from latest to oldest
    return historyItems.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }, [disputeData?.history]);

  // Helper function to get color based on history item type
  const getHistoryItemColor = (type: string) => {
    switch (type) {
      case 'challenge':
        return 'text-red-600';
      case 'challenge-success':
        return 'text-green-600';
      case 'file':
        return 'text-blue-600';
      case 'chargeback':
        return 'text-orange-600';
      case 'representment':
        return 'text-green-600';
      case 'dispute':
        return 'text-purple-600';
      case 'generic':
        return 'text-gray-600';
      default:
        return 'text-gray-800';
    }
  };

  // Update showActionButtons based on dispute status
  useEffect(() => {
    setShowActionButtons(disputeData?.status?.toLowerCase() === 'challenge');
  }, [disputeData?.status]);

  const handleUploadDocument = async (params: DisputeChallengeDocumentParams) => {
    try {
      if (!params.file) {
        toast.error('No file provided');
        return;
      }

      const parsedFiles = await parseFilesToBase64([params.file]);
      if (!parsedFiles.length) {
        toast.error('Failed to parse file');
        return;
      }

      const file = parsedFiles[0];

      // Upload the file using File_upload mutation
      const uploadResult = await fileUploadMutation({
        variables: {
          input: {
            files: [
              {
                filename: params.file.name,
                mimetype: params.file.type,
                b64: file.b64,
              },
            ],
          },
        },
      });

      const fileUrl = uploadResult.data?.file_upload?.files?.[0]?.url;
      if (!fileUrl) {
        toast.error('Failed to get file URL from upload');
        return;
      }

      // Add the file to the state with the URL
      const newFile: DisputeChallengeDocumentParams = {
        file: params.file,
        type: params.type,
        url: fileUrl,
      };

      // Now use the URL to register the file in the dispute system
      const disputeUploadResult = await uploadDisputeFileMutation({
        variables: {
          input: {
            groupID,
            data: {
              disputeID: disputeData?.disputeID ?? '',
              upload: {
                url: fileUrl,
                size: params.file.size,
                type: params.type,
                filetype: params.file.type,
                purpose: params.type,
              },
            },
          },
        },
      });

      if (disputeUploadResult.data?.gateway_uploadDisputeDocument?.itemID) {
        const itemID = disputeUploadResult.data.gateway_uploadDisputeDocument.itemID;
        const finalFile = { ...newFile, itemID };
        setFiles((prevFiles) => [...prevFiles, finalFile]);
        setUploadedItemIDs((prevIds) => [...prevIds, itemID]);
        toast.success('File uploaded successfully');
      } else {
        setFiles((prevFiles) => [...prevFiles, newFile]);
        toast.warning('File uploaded but not linked properly');
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error('Failed to upload file');
    }
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="7xl" className="">
      <div className="overflow-y-auto p-6">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button color="gray" size="sm" onClick={onClose}>
              Back
            </Button>
            <h2 className="text-xl font-semibold">Dispute Details</h2>
          </div>
          <Button color="gray" onClick={onClose} className="p-1">
            <HiX className="h-5 w-5" />
          </Button>
        </div>

        <SpinnerLoading
          isLoading={
            disputeDataLoading ||
            uploadDisputeFileLoading ||
            fileUploadLoading ||
            uploadDocumentLoading
          }
        />
        {!disputeDataLoading && (
          <>
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="font-semibold">#</span>
                <span className="text-lg font-bold uppercase">{disputeData?.disputeID}</span>
                <StatusChip variant={status} label={label} />
                <span className="text-gray-500">Date: {formatDateTime(disputeData?.date)}</span>
                {disputeData?.daysToRepresent !== undefined &&
                  disputeData.daysToRepresent !== null &&
                  disputeData.daysToRepresent < 5 && (
                    <span className="ml-2 rounded-md bg-red-100 px-2 py-1 text-xs font-bold text-red-600">
                      {disputeData.daysToRepresent} days to represent
                    </span>
                  )}
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-2xl font-bold">{moneyFormat(disputeData?.amount)}</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-8">
              <div>
                <GeneralDetailsSection
                  details={[
                    { label: 'Created by', value: disputeData?.createdBy },
                    { label: 'Auth Code', value: disputeData?.authCode },
                    { label: 'Payment type', value: disputeData?.paymentType },
                    { label: 'Entry Method', value: disputeData?.entryMethod },
                    { label: 'Method', value: disputeData?.method },
                    { label: 'Token Source', value: disputeData?.tokenSource },
                    {
                      label: 'Transaction #',
                      value: disputeData?.transactionID,
                      className: 'cursor-pointer hover:underline text-blue-600',
                      onClick: () =>
                        router.push(
                          `/dashboard/reporting/transactions?id=${disputeData?.transactionID}&from=dispute`,
                        ),
                    },
                    {
                      label: 'Batch #',
                      value: disputeData?.batchID,
                      className: 'cursor-pointer hover:underline',
                      onClick: () =>
                        router.push(
                          `/dashboard/reporting/batches?id=${disputeData?.batchID}&from=dispute`,
                        ),
                    },
                    { label: 'GSA', value: disputeData?.gsa },
                    {
                      label: 'Payment Plan',
                      value: disputeData?.paymentPlan,
                      onClick: () =>
                        router.push(
                          `/dashboard/customers/schedule?id=${disputeData?.paymentPlan}&from=dispute`,
                        ),
                    },
                    { label: 'EMV', value: disputeData?.emv },
                    { label: 'Source', value: disputeData?.source },
                    { label: 'Last 4', value: disputeData?.last4 },
                    { label: 'Location', value: disputeData?.location },
                    {
                      label: 'Days to Represent',
                      value:
                        disputeData?.daysToRepresent !== undefined
                          ? String(disputeData.daysToRepresent)
                          : 'N/A',
                      className:
                        disputeData?.daysToRepresent && disputeData.daysToRepresent < 5
                          ? 'text-red-600 font-bold'
                          : '',
                    },
                  ]}
                />
              </div>
              <div>
                <div>
                  <h3 className="mb-4 text-lg font-semibold">Review Details</h3>
                  <ProductListSection
                    products={
                      disputeData?.products as unknown as ProductListSectionProps['products']
                    }
                  />
                </div>
              </div>
            </div>
            <div className="mt-4 flex w-full flex-col gap-4 border-t pt-8 lg:flex-row">
              {/* Dispute History Timeline */}
              <div className="flex-1">
                <h3 className="mb-4 text-lg font-semibold">Dispute History</h3>
                <div className="rounded-lg border border-gray-200 p-4">
                  <Timeline>
                    {generateHistoryItems.length > 0 ? (
                      generateHistoryItems.map((historyItem, index) => (
                        <Timeline.Item key={index}>
                          <Timeline.Point icon={historyItem.icon || HiCalendar} />
                          <Timeline.Content>
                            <Timeline.Time>{formatDateTime(historyItem?.createdAt)}</Timeline.Time>
                            <Timeline.Title>
                              <span className={`${getHistoryItemColor(historyItem.type)}`}>
                                {historyItemActionTitle(historyItem?.action)}
                              </span>
                            </Timeline.Title>
                            <Timeline.Body>
                              <div className="text-sm text-gray-600">
                                {/* <p>{historyItem?.body}</p> */}
                                {historyItem.type === 'challenge' && historyItem.details && (
                                  <div className="mt-2 rounded-md bg-red-50 p-2 text-xs">
                                    <p className="font-semibold">Challenge Error Details:</p>
                                    <p>Error Code: {historyItem.details?.error_code || 'N/A'}</p>
                                    {historyItem.details?.detailed_error_code && (
                                      <p>
                                        Detailed Code: {historyItem.details.detailed_error_code}
                                      </p>
                                    )}
                                    {historyItem.details?.detailed_error_description && (
                                      <p>
                                        Description:{' '}
                                        {historyItem.details.detailed_error_description}
                                      </p>
                                    )}
                                  </div>
                                )}
                                {historyItem.type === 'challenge-success' &&
                                  historyItem.details && (
                                    <div className="mt-2 rounded-md bg-green-50 p-2 text-xs">
                                      <p className="font-semibold">Challenge Success Details:</p>
                                      <p>Status: {historyItem.details?.status || 'SUCCESS'}</p>
                                      {historyItem.details?.document_name && (
                                        <p>Document: {historyItem.details.document_name}</p>
                                      )}
                                      {historyItem.details?.action_type && (
                                        <p>Action: {historyItem.details.action_type}</p>
                                      )}
                                      {historyItem.details?.action_time && (
                                        <p>
                                          Time: {formatDateTime(historyItem.details.action_time)}
                                        </p>
                                      )}
                                      {historyItem.details?.action_result && (
                                        <p>Result: {historyItem.details.action_result}</p>
                                      )}
                                      {historyItem.details?.file_url && (
                                        <p>
                                          File URL:{' '}
                                          <a
                                            href={historyItem.details.file_url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:underline"
                                          >
                                            View File
                                          </a>
                                        </p>
                                      )}
                                    </div>
                                  )}
                                {historyItem.type === 'generic' &&
                                  !['DAYS_TO_REPRESENT_ZERO', 'REPRESENTMENT_DATE'].includes(
                                    historyItem.action?.toUpperCase() ?? '',
                                  ) &&
                                  historyItem.details && (
                                    <div className="mt-2 rounded-md bg-gray-50 p-2 text-xs">
                                      <p className="font-semibold">Event Details:</p>
                                      {Object.entries(historyItem.details)
                                        // Filter out empty or null values
                                        .filter(
                                          ([_, value]) =>
                                            value !== null && value !== undefined && value !== '',
                                        )
                                        .map(([key, value], idx) => (
                                          <p key={idx}>
                                            {/* Capitalize the field name */}
                                            <span className="font-medium">
                                              {key.charAt(0).toUpperCase() +
                                                key.slice(1).replace(/_/g, ' ')}
                                              :
                                            </span>{' '}
                                            {/* Handle React elements (like moneyFormat output) or strings */}
                                            {React.isValidElement(value) ? value : String(value)}
                                          </p>
                                        ))}
                                    </div>
                                  )}
                                <p className="mt-1 text-xs text-gray-500">
                                  By: {historyItem?.actor}
                                </p>
                              </div>
                            </Timeline.Body>
                          </Timeline.Content>
                        </Timeline.Item>
                      ))
                    ) : (
                      <p className="text-center text-gray-500">No history available</p>
                    )}
                  </Timeline>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default DisputeDetailsModal;
