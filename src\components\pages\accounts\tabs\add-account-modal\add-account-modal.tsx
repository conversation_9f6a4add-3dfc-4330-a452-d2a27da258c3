import { Button, Modal, TextInput, Label } from 'flowbite-react';
import { useState } from 'react';
import { toast } from 'react-toastify';

export const AddAccountModal = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [generatedUrl, setGeneratedUrl] = useState('');

  // Generate a realistic UUID for p_id
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c == 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email || !name) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate UUID for the application
      const pId = generateUUID();

      // Construct the Miko page URL with logo parameter
      const mikoUrl = `https://ng-account-fe-dev.dev1.ngnair.com/accounts/new?p_id=${pId}&logo=https://res.cloudinary.com/dkqya1oaz/image/upload/v1753877272/image_suusoy.jpg`;

      // Store the form data in localStorage for potential use
      localStorage.setItem(
        'pendingAccountApplication',
        JSON.stringify({
          email,
          name,
          pId,
          timestamp: new Date().toISOString(),
        }),
      );

      // Set the generated URL to display instead of redirecting
      setGeneratedUrl(mikoUrl);

      toast.success('Account application URL generated successfully!');
    } catch (error) {
      toast.error('Failed to generate application URL');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setEmail('');
    setName('');
    setGeneratedUrl('');
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={handleClose} size="md">
      <Modal.Header>
        <h3 className="text-xl font-semibold text-gray-900">Add New Account</h3>
      </Modal.Header>

      <Modal.Body>
        {!generatedUrl ? (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name" value="Full Name" />
              <TextInput
                id="name"
                type="text"
                placeholder="Enter full name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="email" value="Email Address" />
              <TextInput
                id="email"
                type="email"
                placeholder="Enter email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
          </form>
        ) : (
          <div className="space-y-4">
            <div className="rounded-lg bg-green-50 p-4">
              <h4 className="mb-2 font-semibold text-green-800">
                Account Application URL Generated
              </h4>
              <p className="mb-3 text-sm text-green-700">
                Share this URL with the applicant to complete their account setup:
              </p>
              <div className="rounded border bg-white p-3">
                <code className="break-all text-sm text-gray-800">{generatedUrl}</code>
              </div>
              <div className="mt-3 flex space-x-2">
                <Button
                  size="sm"
                  color="success"
                  onClick={() => navigator.clipboard.writeText(generatedUrl)}
                >
                  Copy URL
                </Button>
                <Button size="sm" color="gray" onClick={() => window.open(generatedUrl, '_blank')}>
                  Open in New Tab
                </Button>
              </div>
            </div>
          </div>
        )}
      </Modal.Body>

      <Modal.Footer>
        <div className="flex w-full justify-between">
          <Button color="gray" onClick={handleClose}>
            {generatedUrl ? 'Close' : 'Cancel'}
          </Button>
          {!generatedUrl && (
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={isSubmitting || !email || !name}
              isProcessing={isSubmitting}
            >
              {isSubmitting ? 'Generating...' : 'Generate Application Link'}
            </Button>
          )}
        </div>
      </Modal.Footer>
    </Modal>
  );
};
