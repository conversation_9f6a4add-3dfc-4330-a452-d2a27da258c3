import { mockAccounts } from './accounts-data';

export interface AccountDetailsMetrics {
  monthlySales: number;
  closedTransactions: number;
  availableBalance: number;
}

export interface AccountLimits {
  cardPerTransaction: number;
  cardMonthly: number;
  cardMonthlyPercentage: number;
  bankTransferPerTransaction: number;
  bankTransferMonthly: number;
  bankTransferMonthlyPercentage: number;
}

export interface AccountBankInfo {
  bankName: string;
  accountLast4: string;
  bankCode: string;
}

export interface AccountDetailsData {
  id: string;
  groupID: string;
  dbaName: string;
  accountStatus: string;
  dateAdded: string;
  paymentType: string;
  email: string;
  phone: string;
  country: string;
  billingAddress: string;
  address: string;
  rate: string;
  metrics: AccountDetailsMetrics;
  limits: AccountLimits;
  bankInfo: AccountBankInfo;
  friendlyName: string;
}

function generateMockAccountDetails(accountId: string): AccountDetailsData | null {
  const baseAccount = mockAccounts.find((acc) => acc.id === accountId);
  if (!baseAccount) return null;

  // Only show minimal information for draft accounts
  // All other statuses (pending, active, approved, rejected) show full details for admin review
  if (baseAccount.status === 'draft' || baseAccount.status === 'sent') {
    return {
      id: baseAccount.id,
      groupID: `GRP-${baseAccount.id}`,
      dbaName: baseAccount.businessLocation,
      friendlyName: baseAccount.businessLocation,
      accountStatus: 'Draft - Incomplete Application',
      dateAdded: baseAccount.lastActive,
      paymentType: 'Disabled',
      email: baseAccount.email,
      phone: baseAccount.phoneNumber,
      country: '[Required] Select Country',
      billingAddress: '[Required] Enter Business Address',
      address: baseAccount.address || '[Required] Enter Business Address',
      rate: baseAccount.rate || '[Not Set]',
      metrics: {
        monthlySales: 0,
        closedTransactions: 0,
        availableBalance: 0,
      },
      limits: {
        cardPerTransaction: 0,
        cardMonthly: 0,
        cardMonthlyPercentage: 0,
        bankTransferPerTransaction: 0,
        bankTransferMonthly: 0,
        bankTransferMonthlyPercentage: 0,
      },
      bankInfo: {
        bankName: '[Not Provided]',
        accountLast4: '[Not Provided]',
        bankCode: '[Not Provided]',
      },
    };
  }

  // For non-draft accounts, return full information
  return {
    id: baseAccount.id,
    groupID: `GRP-${baseAccount.id}`,
    dbaName: baseAccount.businessLocation,
    friendlyName: baseAccount.businessLocation,
    accountStatus: baseAccount.status,
    dateAdded: baseAccount.lastActive,
    paymentType: baseAccount.status === 'active' ? 'enabled' : 'disabled',
    email: baseAccount.email,
    phone: baseAccount.phoneNumber,
    country: 'United States',
    billingAddress: baseAccount.address || '123 Main Street, Suite 100, City, ST 12345',
    address: baseAccount.address || '123 Main Street, Suite 100, City, ST 12345',
    rate: baseAccount.rate || 'N/A',
    metrics: {
      monthlySales: Math.round(baseAccount.totalEarnings / 12),
      closedTransactions: Math.round(baseAccount.totalEarnings / 100),
      availableBalance: Math.round(baseAccount.totalEarnings * 0.15),
    },
    limits: {
      cardPerTransaction: 5000,
      cardMonthly: 50000,
      cardMonthlyPercentage: 45,
      bankTransferPerTransaction: 10000,
      bankTransferMonthly: 100000,
      bankTransferMonthlyPercentage: 25,
    },
    bankInfo: {
      bankName: 'JPMORGAN CHASE BANK, NA',
      accountLast4: '4321',
      bankCode: '*********',
    },
  };
}

export function getMockAccountDetails(accountId: string): AccountDetailsData {
  const details = generateMockAccountDetails(accountId);
  if (!details) {
    throw new Error(`Account with ID ${accountId} not found`);
  }
  return details;
}
