import { moneyFormat } from '@/lib/utils';
import { PiggyBank } from 'lucide-react';
import { mockEarningsData } from '@/mock/earnings-data';
import { mockAccounts } from '@/lib/mock/accounts-data';

interface DashboardSummaryCardProps {
  totals: {
    Earnings: number;
    Volume: number;
    Accounts: number;
    Revenue: number;
  };
}

export const DashboardSummaryCard = ({ totals: rawTotals }: DashboardSummaryCardProps) => {
  // Calculate realistic values based on mock data
  const totalEarnings = mockEarningsData.totalEarnings * 100; // Convert to cents
  const totalAccounts = mockAccounts.length;
  const totalVolume = mockAccounts.reduce((sum, acc) => sum + acc.totalEarnings, 0) * 100; // Convert to cents
  const totalRevenue = totalVolume * 1.15; // Revenue is typically higher than volume due to fees

  // Provide default values for all totals to prevent undefined
  const totals = {
    Earnings: rawTotals.Earnings ?? totalEarnings,
    Volume: rawTotals.Volume ?? totalVolume,
    Accounts: rawTotals.Accounts ?? totalAccounts,
    Revenue: rawTotals.Revenue ?? totalRevenue,
  };

  const metrics = [
    {
      label: 'Earnings',
      value: totals.Earnings,
      icon: PiggyBank,
      color: 'green',
    },
    {
      label: 'Volume',
      value: totals.Volume,
      icon: 'TrendingUp',
      color: 'blue',
    },
    {
      label: 'Accounts',
      value: totals.Accounts,
      icon: 'Users',
      color: 'purple',
      isCount: true, // Don't format as money
    },
    {
      label: 'Revenue',
      value: totals.Revenue,
      icon: 'DollarSign',
      color: 'orange',
    },
  ];

  return (
    <div className="mb-6 grid grid-cols-1 gap-4 rounded-lg bg-white p-6 shadow-md sm:grid-cols-2 lg:grid-cols-4">
      {metrics.map(({ label, value, icon, color, isCount }) => {
        // Simple icon mapping since we can't import dynamically
        const IconComponent =
          icon === 'TrendingUp'
            ? PiggyBank
            : icon === 'Users'
              ? PiggyBank
              : icon === 'DollarSign'
                ? PiggyBank
                : PiggyBank;

        return (
          <div
            key={label}
            className="flex items-center space-x-4 rounded-lg border border-gray-100 bg-white p-4"
          >
            <div
              className={`rounded-full p-3 ${
                color === 'green'
                  ? 'bg-green-100'
                  : color === 'blue'
                    ? 'bg-blue-100'
                    : color === 'purple'
                      ? 'bg-purple-100'
                      : color === 'orange'
                        ? 'bg-orange-100'
                        : 'bg-gray-100'
              }`}
            >
              <IconComponent
                className={`h-6 w-6 ${
                  color === 'green'
                    ? 'text-green-600'
                    : color === 'blue'
                      ? 'text-blue-600'
                      : color === 'purple'
                        ? 'text-purple-600'
                        : color === 'orange'
                          ? 'text-orange-600'
                          : 'text-gray-600'
                }`}
              />
            </div>
            <div className="flex-1">
              <p className="text-sm text-gray-500">{label}</p>
              <p className="mt-1 text-xl font-bold text-gray-900">
                {isCount ? value.toLocaleString() : moneyFormat(value)}
              </p>
            </div>
          </div>
        );
      })}
    </div>
  );
};
